import torch
import torch.nn as nn
import torch.nn.functional as F


class DGAFPrompt(nn.Module):
    def __init__(self, grid_size=16, image_size=512, device='cuda:0'):
        super().__init__()
        self.grid_size = grid_size
        self.image_size = image_size
        self.device = device
        self.num_control_points = grid_size * grid_size
        
        # 控制点参数初始化
        # 位移参数 Δp: [num_points, 2]
        self.delta_p = nn.Parameter(torch.randn(self.num_control_points, 2) * 1.0)
        
        # 外观调整参数 Δa: [num_points, 3] 
        self.delta_a = nn.Parameter(torch.randn(self.num_control_points, 3) * 0.1)
        
        # 影响半径参数 r: [num_points]
        self.radius = nn.Parameter(torch.empty(self.num_control_points).uniform_(0.3, 0.8))

        # 显著性权重参数 α: [num_points]
        self.alpha = nn.Parameter(torch.empty(self.num_control_points).uniform_(0.8, 1.2))

        # 软阈值陡峭度参数
        self.sigmoid_steepness = 10.0

        # 生成控制点网格坐标
        self.register_buffer('control_coords', self._generate_control_grid())
        
    def _generate_control_grid(self):
        """生成16x16控制点网格的归一化坐标"""
        # 生成[0,1]范围内的网格坐标
        x = torch.linspace(0, 1, self.grid_size)
        y = torch.linspace(0, 1, self.grid_size)
        grid_y, grid_x = torch.meshgrid(y, x)

        # 展平为[256, 2]格式
        coords = torch.stack([grid_x.flatten(), grid_y.flatten()], dim=1)
        return coords
        
    def update(self, init_control_points):
        """使用Memory Bank检索到的控制点参数进行完整初始化
        更新所有参数以保持参数间的协调性"""
        with torch.no_grad():
            # init_control_points shape: [1, num_points, 7] 或 [num_points, 7]
            if init_control_points.dim() == 3:
                init_control_points = init_control_points.squeeze(0)

            # 确保在正确的设备上
            init_control_points = init_control_points.to(self.device)

            # 完整更新策略：更新所有参数以保持协调性
            self.delta_p.copy_(init_control_points[:, :2])  # 位移参数
            self.delta_a.copy_(init_control_points[:, 2:5])  # 外观参数
            self.radius.copy_(init_control_points[:, 5])     # 影响半径参数
            self.alpha.copy_(init_control_points[:, 6])      # 显著性权重参数
            
    def _idw_interpolation(self, coords, control_coords, control_values, influence_radii, alpha_weights):
        """反距离加权插值
        Args:
            coords: [H*W, 2] 目标像素坐标
            control_coords: [num_points, 2] 控制点坐标
            control_values: [num_points, channels] 控制点值
            influence_radii: [num_points] 影响半径
            alpha_weights: [num_points] 显著性权重
        Returns:
            interpolated_values: [H*W, channels] 插值结果
        """
        # 计算距离矩阵 [H*W, num_points]
        distances = torch.cdist(coords, control_coords)

        # 应用影响半径约束（使用可微分的软阈值）
        radius_mask = torch.sigmoid(self.sigmoid_steepness * (influence_radii.unsqueeze(0) - distances))

        # 计算IDW权重，避免除零
        epsilon = 1e-8
        weights = 1.0 / (distances + epsilon)
        weights = weights * radius_mask

        # 应用显著性权重
        weights = weights * alpha_weights.unsqueeze(0)

        # 归一化权重
        weight_sum = weights.sum(dim=1, keepdim=True)
        weights = weights / (weight_sum + epsilon)

        # 执行插值
        interpolated_values = torch.matmul(weights, control_values)

        return interpolated_values
        
    def _generate_dense_field(self, image_shape):
        """生成稠密变换场
        Args:
            image_shape: (H, W)
        Returns:
            displacement_field: [H, W, 2] 位移场
            appearance_field: [H, W, 3] 外观场
        """
        H, W = image_shape
        
        # 生成像素坐标网格 [H*W, 2]
        y_coords = torch.linspace(0, 1, H, device=self.device)
        x_coords = torch.linspace(0, 1, W, device=self.device)
        grid_y, grid_x = torch.meshgrid(y_coords, x_coords)
        pixel_coords = torch.stack([grid_x.flatten(), grid_y.flatten()], dim=1)
        
        # 插值生成位移场
        displacement_flat = self._idw_interpolation(
            pixel_coords, self.control_coords, self.delta_p, self.radius, self.alpha
        )
        displacement_field = displacement_flat.view(H, W, 2)
        
        # 插值生成外观场
        appearance_flat = self._idw_interpolation(
            pixel_coords, self.control_coords, self.delta_a, self.radius, self.alpha
        )
        appearance_field = appearance_flat.view(H, W, 3)
        
        return displacement_field, appearance_field

    def forward(self, x):
        """前向传播
        Args:
            x: [B, 3, H, W] 输入图像
        Returns:
            transformed_image: [B, 3, H, W] 变换后图像
            image_summary: [B, 384] 图像摘要
        """
        B, C, H, W = x.shape

        # 生成稠密变换场
        displacement_field, appearance_field = self._generate_dense_field((H, W))

        # 应用几何变换
        transformed_image = self._apply_geometric_transform(x, displacement_field)

        # 应用外观变换
        transformed_image = self._apply_appearance_transform(transformed_image, appearance_field)

        # 生成图像摘要
        image_summary = self._generate_image_summary(x)

        return transformed_image, image_summary

    def _apply_geometric_transform(self, x, displacement_field):
        """应用几何变换
        Args:
            x: [B, 3, H, W] 输入图像
            displacement_field: [H, W, 2] 位移场
        Returns:
            transformed_x: [B, 3, H, W] 几何变换后图像
        """
        B, C, H, W = x.shape

        # 生成采样网格
        # 创建标准化坐标网格 [-1, 1]
        y_coords = torch.linspace(-1, 1, H, device=self.device)
        x_coords = torch.linspace(-1, 1, W, device=self.device)
        grid_y, grid_x = torch.meshgrid(y_coords, x_coords)
        base_grid = torch.stack([grid_x, grid_y], dim=2)  # [H, W, 2]

        # 将位移场从[0,1]坐标转换为[-1,1]坐标系的位移
        # displacement_field是在[0,1]坐标系中的位移，需要转换为像素位移
        pixel_displacement = displacement_field * torch.tensor([W-1, H-1], device=self.device)
        # 转换为[-1,1]坐标系的位移
        normalized_displacement = pixel_displacement / torch.tensor([W-1, H-1], device=self.device) * 2

        # 应用位移
        sampling_grid = base_grid + normalized_displacement

        # 扩展到batch维度
        sampling_grid = sampling_grid.unsqueeze(0).repeat(B, 1, 1, 1)  # [B, H, W, 2]

        # 使用grid_sample进行双线性插值
        transformed_x = F.grid_sample(x, sampling_grid, mode='bilinear',
                                    padding_mode='border', align_corners=True)

        return transformed_x

    def _apply_appearance_transform(self, x, appearance_field):
        """应用外观变换
        Args:
            x: [B, 3, H, W] 输入图像
            appearance_field: [H, W, 3] 外观调整场
        Returns:
            transformed_x: [B, 3, H, W] 外观变换后图像
        """
        B, C, H, W = x.shape

        # 将外观场扩展到batch维度并调整维度顺序
        appearance_adjustment = appearance_field.permute(2, 0, 1).unsqueeze(0)  # [1, 3, H, W]
        appearance_adjustment = appearance_adjustment.repeat(B, 1, 1, 1)  # [B, 3, H, W]

        # 应用外观调整
        transformed_x = x + appearance_adjustment

        # 确保像素值在合理范围内
        transformed_x = torch.clamp(transformed_x, 0.0, 1.0)

        return transformed_x

    def _generate_image_summary(self, x):
        """生成图像摘要：8x8分块的均值和标准差
        Args:
            x: [B, 3, H, W] 输入图像
        Returns:
            summary: [B, 384] 图像摘要 (8*8*6=384维)
        """
        B, C, H, W = x.shape

        # 计算分块大小
        block_h = H // 8
        block_w = W // 8

        summaries = []

        for b in range(B):
            block_stats = []

            for i in range(8):
                for j in range(8):
                    # 提取分块
                    start_h, end_h = i * block_h, (i + 1) * block_h
                    start_w, end_w = j * block_w, (j + 1) * block_w
                    block = x[b, :, start_h:end_h, start_w:end_w]  # [3, block_h, block_w]

                    # 计算均值和标准差
                    block_mean = block.mean(dim=(1, 2))  # [3]
                    block_std = block.std(dim=(1, 2))   # [3]

                    # 合并统计量
                    block_stat = torch.cat([block_mean, block_std])  # [6]
                    block_stats.append(block_stat)

            # 合并所有分块统计量
            image_summary = torch.cat(block_stats)  # [8*8*6=384]
            summaries.append(image_summary)

        return torch.stack(summaries)  # [B, 384]
