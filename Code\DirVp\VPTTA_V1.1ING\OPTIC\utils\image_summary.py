import torch
import torch.nn as nn


class ImageSummary:
    """图像摘要生成器：计算8x8分块的均值和标准差"""
    
    def __init__(self, summary_blocks=8):
        self.summary_blocks = summary_blocks
        
    def __call__(self, x):
        """生成图像摘要
        Args:
            x: [B, 3, H, W] 输入图像
        Returns:
            summary: [B, 384] 图像摘要 (8*8*6=384维)
        """
        return self.generate_summary(x)
        
    def generate_summary(self, x):
        """生成图像摘要：8x8分块的均值和标准差
        Args:
            x: [B, 3, H, W] 输入图像
        Returns:
            summary: [B, 384] 图像摘要 (8*8*6=384维)
        """
        B, C, H, W = x.shape
        
        # 计算分块大小
        block_h = H // self.summary_blocks
        block_w = W // self.summary_blocks
        
        summaries = []
        
        for b in range(B):
            block_stats = []
            
            for i in range(self.summary_blocks):
                for j in range(self.summary_blocks):
                    # 提取分块
                    start_h, end_h = i * block_h, (i + 1) * block_h
                    start_w, end_w = j * block_w, (j + 1) * block_w
                    block = x[b, :, start_h:end_h, start_w:end_w]  # [3, block_h, block_w]
                    
                    # 计算均值和标准差
                    block_mean = block.mean(dim=(1, 2))  # [3]
                    block_std = block.std(dim=(1, 2))   # [3]
                    
                    # 合并统计量
                    block_stat = torch.cat([block_mean, block_std])  # [6]
                    block_stats.append(block_stat)
            
            # 合并所有分块统计量
            image_summary = torch.cat(block_stats)  # [8*8*6=384]
            summaries.append(image_summary)
        
        return torch.stack(summaries)  # [B, 384]
        
    def get_summary_dimension(self):
        """返回摘要向量的维度"""
        return self.summary_blocks * self.summary_blocks * 6  # 8*8*6=384
