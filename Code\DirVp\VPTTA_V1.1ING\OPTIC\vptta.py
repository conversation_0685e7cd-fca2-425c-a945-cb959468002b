import os
import torch
import numpy as np
import argparse, sys, datetime
from config import Logger
from torch.autograd import Variable
from utils.convert import AdaBN
from utils.memory import Memory
from utils.dgaf_prompt import DGAFPrompt
from utils.image_summary import ImageSummary
from utils.physics_loss import PhysicsConstrainedLoss
from utils.metrics import calculate_metrics
from networks.ResUnet_TTA import ResUnet
from torch.utils.data import DataLoader
from dataloaders.OPTIC_dataloader import OPTIC_dataset
from dataloaders.transform import collate_fn_wo_transform
from dataloaders.convert_csv_to_list import convert_labeled_list


torch.set_num_threads(1)


class VPTTA:
    def __init__(self, config):
        # Save Log
        time_now = datetime.datetime.now().__format__("%Y%m%d_%H%M%S_%f")
        log_root = os.path.join(config.path_save_log, 'VPTTA')
        if not os.path.exists(log_root):
            os.makedirs(log_root)
        log_path = os.path.join(log_root, time_now + '.log')
        sys.stdout = Logger(log_path, sys.stdout)

        # Data Loading
        target_test_csv = []
        for target in config.Target_Dataset:
            if target != 'REFUGE_Valid':
                target_test_csv.append(target + '_train.csv')
                target_test_csv.append(target + '_test.csv')
            else:
                target_test_csv.append(target + '.csv')
        ts_img_list, ts_label_list = convert_labeled_list(config.dataset_root, target_test_csv)
        target_test_dataset = OPTIC_dataset(config.dataset_root, ts_img_list, ts_label_list,
                                            config.image_size, img_normalize=True)
        self.target_test_loader = DataLoader(dataset=target_test_dataset,
                                             batch_size=config.batch_size,
                                             shuffle=False,
                                             pin_memory=True,
                                             drop_last=False,
                                             collate_fn=collate_fn_wo_transform,
                                             num_workers=config.num_workers)
        self.image_size = config.image_size

        # Model
        self.load_model = os.path.join(config.model_root, str(config.Source_Dataset))  # Pre-trained Source Model
        self.backbone = config.backbone
        self.in_ch = config.in_ch
        self.out_ch = config.out_ch

        # Optimizer
        self.optim = config.optimizer
        self.lr = config.lr
        self.weight_decay = config.weight_decay
        self.momentum = config.momentum
        self.betas = (config.beta1, config.beta2)

        # GPU
        self.device = config.device

        # Warm-up
        self.warm_n = config.warm_n

        # DGAF-Prompt参数
        self.control_grid_size = config.control_grid_size
        self.sparsity_lambda = config.sparsity_lambda
        self.summary_blocks = config.summary_blocks
        self.volume_lambda = config.volume_lambda
        self.smooth_lambda = config.smooth_lambda
        self.iters = config.iters

        # Initialize the pre-trained model and optimizer
        self.build_model()

        # Memory Bank
        self.neighbor = config.neighbor
        self.image_summary = ImageSummary(summary_blocks=self.summary_blocks)
        summary_dim = self.image_summary.get_summary_dimension()
        self.memory_bank = Memory(size=config.memory_size, dimension=summary_dim)

        # Print Information
        print("[DEBUG] [INIT] Configuration parameters:")
        for arg, value in vars(config).items():
            print(f"[DEBUG] [INIT] {arg}: {value}")
        self.print_prompt()
        print("[DEBUG] [INIT] " + '***' * 20)

    def build_model(self):
        self.prompt = DGAFPrompt(grid_size=self.control_grid_size, image_size=self.image_size, device=self.device).to(self.device)
        self.model = ResUnet(resnet=self.backbone, num_classes=self.out_ch, pretrained=False, newBN=AdaBN, warm_n=self.warm_n).to(self.device)
        checkpoint = torch.load(os.path.join(self.load_model, 'last-Res_Unet.pth'))
        self.model.load_state_dict(checkpoint, strict=True)

        # 初始化物理约束损失
        self.physics_loss = PhysicsConstrainedLoss(
            sparsity_lambda=self.sparsity_lambda,
            volume_lambda=self.volume_lambda,
            smooth_lambda=self.smooth_lambda
        )

        if self.optim == 'SGD':
            self.optimizer = torch.optim.SGD(
                self.prompt.parameters(),
                lr=self.lr,
                momentum=self.momentum,
                nesterov=True,
                weight_decay=self.weight_decay
            )
        elif self.optim == 'Adam':
            self.optimizer = torch.optim.Adam(
                self.prompt.parameters(),
                lr=self.lr,
                betas=self.betas,
                weight_decay=self.weight_decay
            )

    def _log_gradient_stats(self):
        """计算和输出梯度统计信息"""
        total_norm = 0.0
        param_count = 0
        grad_stats = {}

        # 首先检查所有参数的梯度状态
        print(f"[DEBUG] [GRADIENT] Parameter gradient status:")
        for name, param in self.prompt.named_parameters():
            has_grad = param.grad is not None
            grad_norm = param.grad.data.norm(2).item() if has_grad else 0.0
            print(f"[DEBUG] [GRADIENT]   {name}: has_grad={has_grad}, norm={grad_norm:.6f}")

            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1
                grad_stats[name] = {
                    'norm': param_norm.item(),
                    'mean': param.grad.data.mean().item(),
                    'std': param.grad.data.std().item(),
                    'max': param.grad.data.max().item(),
                    'min': param.grad.data.min().item()
                }

        total_norm = total_norm ** (1. / 2)
        print(f"[DEBUG] [GRADIENT] Total gradient norm: {total_norm:.6f}")
        for name, stats in grad_stats.items():
            print(f"[DEBUG] [GRADIENT] {name}: norm={stats['norm']:.6f}, mean={stats['mean']:.6f}, std={stats['std']:.6f}")

    def _log_prompt_stats(self):
        """计算和输出prompt参数统计信息"""
        with torch.no_grad():
            # Delta_p (位移参数) 统计
            delta_p_norm = torch.norm(self.prompt.delta_p, dim=1).mean().item()
            delta_p_max = torch.norm(self.prompt.delta_p, dim=1).max().item()
            print(f"[DEBUG] [PROMPT] delta_p: avg_norm={delta_p_norm:.4f}, max_norm={delta_p_max:.4f}")

            # Delta_a (外观参数) 统计
            delta_a_mean = self.prompt.delta_a.mean().item()
            delta_a_std = self.prompt.delta_a.std().item()
            print(f"[DEBUG] [PROMPT] delta_a: mean={delta_a_mean:.4f}, std={delta_a_std:.4f}")

            # Radius 统计
            radius_mean = self.prompt.radius.mean().item()
            radius_std = self.prompt.radius.std().item()
            radius_range = f"[{self.prompt.radius.min().item():.2f}, {self.prompt.radius.max().item():.2f}]"
            print(f"[DEBUG] [PROMPT] radius: mean={radius_mean:.2f}, std={radius_std:.2f}, range={radius_range}")

            # Alpha 统计
            alpha_mean = self.prompt.alpha.mean().item()
            alpha_std = self.prompt.alpha.std().item()
            alpha_active = (self.prompt.alpha > 0.1).sum().item()
            print(f"[DEBUG] [PROMPT] alpha: mean={alpha_mean:.4f}, std={alpha_std:.4f}, active_points={alpha_active}/{self.prompt.alpha.numel()}")

    def _log_loss_details(self, loss_dict):
        """格式化输出损失详情"""
        print(f"[DEBUG] [LOSS] BN Loss: {loss_dict['bn_loss']:.6f}")
        print(f"[DEBUG] [LOSS] Sparsity Loss: {loss_dict['sparsity_loss']:.6f}")
        print(f"[DEBUG] [LOSS] Volume Loss: {loss_dict['volume_loss']:.6f}")
        print(f"[DEBUG] [LOSS] Smooth Loss: {loss_dict['smooth_loss']:.6f}")
        print(f"[DEBUG] [LOSS] Total Loss: {loss_dict['total_loss']:.6f}")

    def _log_metrics(self, batch_idx, metrics):
        """格式化输出指标信息"""
        disc_dice = metrics[0][0] if len(metrics[0]) > 0 else 0
        cup_dice = metrics[2][0] if len(metrics[2]) > 0 else 0
        disc_asd = metrics[1][0] if len(metrics[1]) > 0 else 0
        cup_asd = metrics[3][0] if len(metrics[3]) > 0 else 0
        mean_dice = (disc_dice + cup_dice) / 2

        print(f"[DEBUG] [METRICS] Batch {batch_idx}: Disc_Dice={disc_dice:.2f}%, Cup_Dice={cup_dice:.2f}%, Mean_Dice={mean_dice:.2f}%")
        print(f"[DEBUG] [METRICS] Batch {batch_idx}: Disc_ASD={disc_asd:.2f}, Cup_ASD={cup_asd:.2f}")

    def print_prompt(self):
        num_params = 0
        for p in self.prompt.parameters():
            num_params += p.numel()
        print("[DEBUG] [INIT] The number of total DGAF-Prompt parameters: {}".format(num_params))

        # 验证参数结构和优化器包含情况
        print("[DEBUG] [INIT] Parameter breakdown:")
        print(f"[DEBUG] [INIT]   delta_p (displacement): {self.prompt.delta_p.numel()} params")
        print(f"[DEBUG] [INIT]   delta_a (appearance): {self.prompt.delta_a.numel()} params")
        print(f"[DEBUG] [INIT]   radius: {self.prompt.radius.numel()} params")
        print(f"[DEBUG] [INIT]   alpha: {self.prompt.alpha.numel()} params")
        expected = self.control_grid_size * self.control_grid_size * 7  # 16*16*7 = 1792
        print(f"[DEBUG] [INIT]   Expected total: {expected} params")
        assert num_params == expected, f"Parameter count mismatch: {num_params} != {expected}"

        # 检查优化器参数
        print("[DEBUG] [INIT] Optimizer parameter groups:")
        for i, param_group in enumerate(self.optimizer.param_groups):
            print(f"[DEBUG] [INIT]   Group {i}: {len(param_group['params'])} parameters")
            for j, param in enumerate(param_group['params']):
                print(f"[DEBUG] [INIT]     Param {j}: shape={param.shape}, requires_grad={param.requires_grad}")

        # 验证每个参数是否在优化器中
        optimizer_params = set()
        for param_group in self.optimizer.param_groups:
            for param in param_group['params']:
                optimizer_params.add(id(param))

        print("[DEBUG] [INIT] Parameter inclusion in optimizer:")
        for name, param in self.prompt.named_parameters():
            in_optimizer = id(param) in optimizer_params
            print(f"[DEBUG] [INIT]   {name}: in_optimizer={in_optimizer}, requires_grad={param.requires_grad}")

    def run(self):
        metric_dict = ['Disc_Dice', 'Disc_ASD', 'Cup_Dice', 'Cup_ASD']

        # Valid on Target
        metrics_test = [[], [], [], []]

        print(f"[DEBUG] [TRAINING] Starting VPTTA training on {len(self.target_test_loader)} batches")
        print(f"[DEBUG] [TRAINING] Memory bank size: {len(self.memory_bank.memory.keys())}/{self.memory_bank.size}")
        print(f"[DEBUG] [TRAINING] Using {self.neighbor} neighbors for initialization")

        for batch, data in enumerate(self.target_test_loader):
            x, y = data['data'], data['mask']
            x = torch.from_numpy(x).to(dtype=torch.float32)
            y = torch.from_numpy(y).to(dtype=torch.float32)

            x, y = Variable(x).to(self.device), Variable(y).to(self.device)

            self.model.eval()
            self.prompt.train()
            self.model.change_BN_status(new_sample=True)

            # Initialize Prompt
            if len(self.memory_bank.memory.keys()) >= self.neighbor:
                # 生成图像摘要用于检索
                image_summary = self.image_summary.generate_summary(x)
                init_data, score = self.memory_bank.get_neighbours(keys=image_summary.cpu().numpy(), k=self.neighbor)
                print(f"[DEBUG] [MEMORY] Batch {batch}: Retrieved from memory bank, avg_score={score.mean():.4f}")
                self.prompt.update(init_data)
            else:
                # 默认初始化：使用合理的随机值而不是全零
                num_points = self.control_grid_size * self.control_grid_size
                # 创建合理的初始化数据
                init_data = torch.zeros((1, num_points, 7))
                # 位移参数：增加初始化幅度以激活物理约束
                init_data[0, :, :2] = torch.randn(num_points, 2) * 0.05
                # 外观参数：小的随机值
                init_data[0, :, 2:5] = torch.randn(num_points, 3) * 0.01
                # 半径参数：合理的范围
                init_data[0, :, 5] = torch.empty(num_points).uniform_(0.3, 0.8)
                # alpha参数：接近1的值
                init_data[0, :, 6] = torch.empty(num_points).uniform_(0.8, 1.2)

                print(f"[DEBUG] [MEMORY] Batch {batch}: Using reasonable random initialization (memory bank size: {len(self.memory_bank.memory.keys())})")
                self.prompt.update(init_data)

            # Train Prompt for n iters (1 iter in our VPTTA)
            for tr_iter in range(self.iters):
                prompt_x, image_summary = self.prompt(x)
                self.model(prompt_x)
                times, bn_loss = 0, 0
                for nm, m in self.model.named_modules():
                    if isinstance(m, AdaBN):
                        bn_loss += m.bn_loss
                        times += 1
                bn_loss = bn_loss / times

                # 获取位移场用于物理约束
                displacement_field, _ = self.prompt._generate_dense_field((x.shape[2], x.shape[3]))

                # 调试：检查距离和radius的关系
                if batch < 3 and tr_iter == 0:  # 只在前几个batch输出
                    H, W = x.shape[2], x.shape[3]
                    y_coords = torch.linspace(0, 1, H, device=self.device)
                    x_coords = torch.linspace(0, 1, W, device=self.device)
                    grid_y, grid_x = torch.meshgrid(y_coords, x_coords)
                    pixel_coords = torch.stack([grid_x.flatten(), grid_y.flatten()], dim=1)

                    # 计算距离矩阵
                    distances = torch.cdist(pixel_coords, self.prompt.control_coords)

                    print(f"[DEBUG] [DISTANCE] Batch {batch}:")
                    print(f"[DEBUG] [DISTANCE]   Distance stats: min={distances.min():.6f}, max={distances.max():.6f}, mean={distances.mean():.6f}")
                    print(f"[DEBUG] [DISTANCE]   Radius stats: min={self.prompt.radius.min():.6f}, max={self.prompt.radius.max():.6f}, mean={self.prompt.radius.mean():.6f}")

                    # 检查mask的分布
                    radius_mask = distances <= self.prompt.radius.unsqueeze(0)
                    mask_ratio = radius_mask.float().mean()
                    print(f"[DEBUG] [DISTANCE]   Mask ratio (True): {mask_ratio:.6f}")

                    # 检查有多少控制点对每个像素有影响
                    active_points_per_pixel = radius_mask.sum(dim=1).float()
                    print(f"[DEBUG] [DISTANCE]   Active points per pixel: min={active_points_per_pixel.min():.1f}, max={active_points_per_pixel.max():.1f}, mean={active_points_per_pixel.mean():.1f}")

                # 计算总损失（包含物理约束）
                total_loss, loss_dict = self.physics_loss(bn_loss, self.prompt.alpha, displacement_field)

                # 输出损失详情
                print(f"[DEBUG] [TRAINING] Batch {batch}, Iter {tr_iter}:")
                self._log_loss_details(loss_dict)

                self.optimizer.zero_grad()
                total_loss.backward()

                # 输出梯度统计
                self._log_gradient_stats()

                self.optimizer.step()

                # 输出prompt参数统计
                self._log_prompt_stats()

                self.model.change_BN_status(new_sample=False)

            # Inference
            self.model.eval()
            self.prompt.eval()
            with torch.no_grad():
                prompt_x, image_summary = self.prompt(x)
                pred_logit, _, _ = self.model(prompt_x)

            # Update the Memory Bank
            # 获取当前优化后的控制点参数，组装为[256, 7]格式
            control_points = torch.cat([
                self.prompt.delta_p,      # [256, 2]
                self.prompt.delta_a,      # [256, 3]
                self.prompt.radius.unsqueeze(1),  # [256, 1]
                self.prompt.alpha.unsqueeze(1)    # [256, 1]
            ], dim=1).unsqueeze(0).detach().cpu().numpy()  # [1, 256, 7]

            self.memory_bank.push(keys=image_summary.cpu().numpy(), control_points=control_points)
            print(f"[DEBUG] [MEMORY] Batch {batch}: Updated memory bank, current size: {len(self.memory_bank.memory.keys())}/{self.memory_bank.size}")

            # Calculate the metrics
            seg_output = torch.sigmoid(pred_logit)
            metrics = calculate_metrics(seg_output.detach().cpu(), y.detach().cpu())

            # 输出当前batch的指标
            self._log_metrics(batch, metrics)

            for i in range(len(metrics)):
                assert isinstance(metrics[i], list), "The metrics value is not list type."
                metrics_test[i] += metrics[i]

        test_metrics_y = np.mean(metrics_test, axis=1)
        print_test_metric_mean = {}
        for i in range(len(test_metrics_y)):
            print_test_metric_mean[metric_dict[i]] = test_metrics_y[i]

        print("[DEBUG] [FINAL] " + "="*50)
        print("[DEBUG] [FINAL] VPTTA Training Completed")
        print("[DEBUG] [FINAL] Final Test Metrics:")
        for metric_name, value in print_test_metric_mean.items():
            print(f"[DEBUG] [FINAL]   {metric_name}: {value:.4f}")
        mean_dice = (print_test_metric_mean['Disc_Dice'] + print_test_metric_mean['Cup_Dice']) / 2
        print(f"[DEBUG] [FINAL]   Mean Dice: {mean_dice:.4f}")
        print(f"[DEBUG] [FINAL] Final Memory Bank Size: {len(self.memory_bank.memory.keys())}/{self.memory_bank.size}")
        print("[DEBUG] [FINAL] " + "="*50)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # Dataset
    parser.add_argument('--Source_Dataset', type=str, default='RIM_ONE_r3',
                        help='RIM_ONE_r3/REFUGE/ORIGA/REFUGE_Valid/Drishti_GS')
    parser.add_argument('--Target_Dataset', type=list)

    parser.add_argument('--num_workers', type=int, default=8)
    parser.add_argument('--image_size', type=int, default=512)

    # Model
    parser.add_argument('--backbone', type=str, default='resnet34', help='resnet34/resnet50')
    parser.add_argument('--in_ch', type=int, default=3)
    parser.add_argument('--out_ch', type=int, default=2)

    # Optimizer
    parser.add_argument('--optimizer', type=str, default='Adam', help='SGD/Adam')
    parser.add_argument('--lr', type=float, default=0.05)
    parser.add_argument('--momentum', type=float, default=0.99)  # momentum in SGD
    parser.add_argument('--beta1', type=float, default=0.9)      # beta1 in Adam
    parser.add_argument('--beta2', type=float, default=0.99)     # beta2 in Adam.
    parser.add_argument('--weight_decay', type=float, default=0.00)

    # Training
    parser.add_argument('--batch_size', type=int, default=1)
    parser.add_argument('--iters', type=int, default=1)

    # Hyperparameters in memory bank and warm-up statistics
    parser.add_argument('--memory_size', type=int, default=40)
    parser.add_argument('--neighbor', type=int, default=16)
    parser.add_argument('--warm_n', type=int, default=5)

    # DGAF-Prompt parameters
    parser.add_argument('--control_grid_size', type=int, default=16)
    parser.add_argument('--sparsity_lambda', type=float, default=0.0001)
    parser.add_argument('--summary_blocks', type=int, default=8)
    parser.add_argument('--volume_lambda', type=float, default=0.1)
    parser.add_argument('--smooth_lambda', type=float, default=0.05)

    # Path
    parser.add_argument('--path_save_log', type=str, default='./logs')
    parser.add_argument('--model_root', type=str, default='./models')
    parser.add_argument('--dataset_root', type=str, default='/media/userdisk0/zychen/Datasets/Fundus')

    # Cuda (default: the first available device)
    parser.add_argument('--device', type=str, default='cuda:0')

    config = parser.parse_args()

    config.Target_Dataset = ['RIM_ONE_r3', 'REFUGE', 'ORIGA', 'REFUGE_Valid', 'Drishti_GS']
    config.Target_Dataset.remove(config.Source_Dataset)

    TTA = VPTTA(config)
    TTA.run()
